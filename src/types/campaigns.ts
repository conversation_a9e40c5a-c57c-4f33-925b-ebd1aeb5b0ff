import type { Database, CampaignData } from './supabase';

// Re-export the campaign data type for convenience
export type { CampaignData } from './supabase';

// Database row types
export type CampaignRow = Database['public']['Tables']['campaigns']['Row'];
export type CampaignInsert = Database['public']['Tables']['campaigns']['Insert'];
export type CampaignUpdate = Database['public']['Tables']['campaigns']['Update'];

// Utility types
export type CampaignTags = {
  id: number;
  name: string;
  tags: string[];
};
